/**
 * 影视搜索Edge Function
 * 集成TMDB API，提供电影和电视剧搜索功能
 * 支持模糊搜索、分页、缓存等功能
 */

import { handleCors, createCorsResponse, createErrorResponse } from '../_shared/cors.ts'
import {
  getRequiredEnv,
  safeParse<PERSON>son,
  validateMethod,
  logRequest,
  retry
} from '../_shared/utils.ts'
import { searchCache, generateSearchCacheKey } from '../_shared/cache.ts'

// TMDB API配置
const TMDB_BASE_URL = 'https://api.themoviedb.org/3'
const TMDB_IMAGE_BASE_URL = 'https://image.tmdb.org/t/p'

// 接口定义
interface SearchRequest {
  query: string
  page?: number
  include_adult?: boolean
  language?: string
  type?: 'movie' | 'tv' | 'multi'
}

interface TMDBMovie {
  id: number
  title?: string
  name?: string
  original_title?: string
  original_name?: string
  overview: string
  poster_path: string | null
  backdrop_path: string | null
  release_date?: string
  first_air_date?: string
  vote_average: number
  vote_count: number
  popularity: number
  genre_ids: number[]
  adult: boolean
  original_language: string
  media_type?: string
}

interface TMDBSearchResponse {
  page: number
  results: TMDBMovie[]
  total_pages: number
  total_results: number
}

interface ProcessedMovie {
  id: number
  title: string
  original_title: string
  overview: string
  poster_url: string | null
  backdrop_url: string | null
  release_date: string | null
  vote_average: number
  vote_count: number
  popularity: number
  genre_ids: number[]
  media_type: string
  language: string
}

/**
 * 处理图片URL
 */
function processImageUrl(path: string | null, size: string = 'w500'): string | null {
  if (!path) return null
  return `${TMDB_IMAGE_BASE_URL}/${size}${path}`
}

/**
 * 处理TMDB电影数据
 */
function processMovieData(movie: TMDBMovie): ProcessedMovie {
  return {
    id: movie.id,
    title: movie.title || movie.name || '',
    original_title: movie.original_title || movie.original_name || '',
    overview: movie.overview || '',
    poster_url: processImageUrl(movie.poster_path),
    backdrop_url: processImageUrl(movie.backdrop_path, 'w1280'),
    release_date: movie.release_date || movie.first_air_date || null,
    vote_average: movie.vote_average || 0,
    vote_count: movie.vote_count || 0,
    popularity: movie.popularity || 0,
    genre_ids: movie.genre_ids || [],
    media_type: movie.media_type || (movie.title ? 'movie' : 'tv'),
    language: movie.original_language || 'unknown'
  }
}

/**
 * 调用TMDB搜索API
 */
async function searchTMDB(params: SearchRequest): Promise<TMDBSearchResponse> {
  const apiKey = getRequiredEnv('TMDB_API_KEY')

  // 构建搜索URL（不再使用api_key参数）
  const searchUrl = new URL(`${TMDB_BASE_URL}/search/${params.type || 'multi'}`)
  searchUrl.searchParams.set('query', params.query)
  searchUrl.searchParams.set('page', String(params.page || 1))
  searchUrl.searchParams.set('include_adult', String(params.include_adult || false))
  searchUrl.searchParams.set('language', params.language || 'zh-CN')

  console.log(`Searching TMDB: ${searchUrl.toString()}`)

  const response = await fetch(searchUrl.toString(), {
    method: 'GET',
    headers: {
      'Accept': 'application/json',
      'Authorization': `Bearer ${apiKey}`,
      'User-Agent': 'NarratoAI/1.0'
    }
  })

  if (!response.ok) {
    const errorText = await response.text()
    console.error(`TMDB API error: ${response.status} ${response.statusText}`, errorText)
    throw new Error(`TMDB API请求失败: ${response.status} ${response.statusText}`)
  }

  return await response.json()
}

/**
 * 主处理函数
 */
Deno.serve(async (req: Request) => {
  try {
    // 处理CORS预检请求
    const corsResponse = handleCors(req)
    if (corsResponse) return corsResponse

    // 记录请求
    logRequest(req, 'movie-search')

    // 验证请求方法
    validateMethod(req, ['GET', 'POST'])

    let searchParams: SearchRequest

    if (req.method === 'GET') {
      // 从URL参数获取搜索条件
      const url = new URL(req.url)
      const query = url.searchParams.get('query')
      
      if (!query || query.trim().length === 0) {
        return createErrorResponse('搜索关键词不能为空', 400)
      }

      searchParams = {
        query: query.trim(),
        page: parseInt(url.searchParams.get('page') || '1'),
        include_adult: url.searchParams.get('include_adult') === 'true',
        language: url.searchParams.get('language') || 'zh-CN',
        type: (url.searchParams.get('type') as 'movie' | 'tv' | 'multi') || 'multi'
      }
    } else {
      // 从请求体获取搜索条件
      const body = await safeParseJson(req)
      
      if (!body.query || body.query.trim().length === 0) {
        return createErrorResponse('搜索关键词不能为空', 400)
      }

      searchParams = {
        query: body.query.trim(),
        page: body.page || 1,
        include_adult: body.include_adult || false,
        language: body.language || 'zh-CN',
        type: body.type || 'multi'
      }
    }

    // 验证参数
    if ((searchParams.page || 1) < 1 || (searchParams.page || 1) > 1000) {
      return createErrorResponse('页码必须在1-1000之间', 400)
    }

    if (searchParams.query.length > 100) {
      return createErrorResponse('搜索关键词长度不能超过100个字符', 400)
    }

    // 生成缓存键
    const cacheKey = generateSearchCacheKey(
      searchParams.query,
      searchParams.page || 1,
      searchParams.type || 'multi',
      searchParams.language || 'zh-CN'
    )

    // 尝试从缓存获取结果
    const cachedResult = searchCache.get(cacheKey)
    if (cachedResult) {
      console.log(`Cache hit for search: "${searchParams.query}"`)
      return createCorsResponse({
        ...cachedResult,
        cached: true,
        timestamp: new Date().toISOString()
      })
    }

    // 调用TMDB API（带重试机制）
    const tmdbResponse = await retry(
      () => searchTMDB(searchParams),
      3,
      1000
    )

    // 处理搜索结果
    const processedResults = tmdbResponse.results.map(processMovieData)

    // 构建响应数据
    const responseData = {
      success: true,
      data: {
        page: tmdbResponse.page,
        results: processedResults,
        total_pages: tmdbResponse.total_pages,
        total_results: tmdbResponse.total_results,
        query: searchParams.query
      },
      cached: false,
      timestamp: new Date().toISOString()
    }

    // 缓存结果（只缓存成功的搜索结果）
    if (processedResults.length > 0) {
      searchCache.set(cacheKey, responseData, 300000) // 缓存5分钟
    }

    console.log(`Search completed: ${processedResults.length} results for "${searchParams.query}"`)

    return createCorsResponse(responseData)

  } catch (error) {
    console.error('Movie search error:', error)
    
    // 根据错误类型返回不同的错误信息
    if (error.message.includes('TMDB_API_KEY')) {
      return createErrorResponse('服务配置错误，请联系管理员', 500)
    }
    
    if (error.message.includes('TMDB API')) {
      return createErrorResponse('电影数据服务暂时不可用，请稍后重试', 503)
    }
    
    if (error.message.includes('Invalid JSON')) {
      return createErrorResponse('请求格式错误', 400)
    }
    
    if (error.message.includes('not allowed')) {
      return createErrorResponse(error.message, 405)
    }

    return createErrorResponse('搜索服务暂时不可用，请稍后重试', 500)
  }
})
