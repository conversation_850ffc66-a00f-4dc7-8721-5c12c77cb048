// 搜索功能调试脚本
// 在浏览器控制台中运行此脚本来测试搜索功能

console.log('🔧 搜索调试工具已加载');

// 测试API连接
async function testApiConnection() {
    console.log('🔗 测试API连接...');
    
    const SUPABASE_URL = 'https://poijzwpvytxbhssbrrew.supabase.co';
    const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBvaWp6d3B2eXR4Ymhzc2JycmV3Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA3NTE1ODksImV4cCI6MjA2NjMyNzU4OX0.PiPSm8IepAF1_zj4Oa0h8hWmoh1OAm-06Ths-6iOuhE';
    
    try {
        const response = await fetch(`${SUPABASE_URL}/functions/v1/movie-search?query=test&page=1&type=multi`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
                'Content-Type': 'application/json'
            }
        });
        
        if (response.ok) {
            const data = await response.json();
            console.log('✅ API连接成功:', data);
            return true;
        } else {
            console.error('❌ API连接失败:', response.status, response.statusText);
            return false;
        }
    } catch (error) {
        console.error('❌ API连接错误:', error);
        return false;
    }
}

// 测试搜索功能
async function testSearch(query = '流浪地球') {
    console.log(`🔍 测试搜索功能: "${query}"`);
    
    const SUPABASE_URL = 'https://poijzwpvytxbhssbrrew.supabase.co';
    const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBvaWp6d3B2eXR4Ymhzc2JycmV3Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA3NTE1ODksImV4cCI6MjA2NjMyNzU4OX0.PiPSm8IepAF1_zj4Oa0h8hWmoh1OAm-06Ths-6iOuhE';
    
    const startTime = Date.now();
    
    try {
        // 测试POST方式
        console.log('📤 尝试POST方式...');
        const postResponse = await fetch(`${SUPABASE_URL}/functions/v1/movie-search`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                query: query.trim(),
                page: 1,
                type: 'multi',
                language: 'zh-CN',
                include_adult: false
            })
        });
        
        const postDuration = Date.now() - startTime;
        
        if (postResponse.ok) {
            const data = await postResponse.json();
            console.log(`✅ POST搜索成功 (${postDuration}ms):`, data);
            return data;
        } else {
            console.error(`❌ POST搜索失败 (${postDuration}ms):`, postResponse.status, postResponse.statusText);
        }
    } catch (error) {
        console.error('❌ POST搜索错误:', error);
    }
    
    try {
        // 测试GET方式
        console.log('📥 尝试GET方式...');
        const getStartTime = Date.now();
        const params = new URLSearchParams({
            query: query.trim(),
            page: '1',
            type: 'multi',
            language: 'zh-CN',
            include_adult: 'false'
        });
        
        const getResponse = await fetch(`${SUPABASE_URL}/functions/v1/movie-search?${params}`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
                'Content-Type': 'application/json'
            }
        });
        
        const getDuration = Date.now() - getStartTime;
        
        if (getResponse.ok) {
            const data = await getResponse.json();
            console.log(`✅ GET搜索成功 (${getDuration}ms):`, data);
            return data;
        } else {
            console.error(`❌ GET搜索失败 (${getDuration}ms):`, getResponse.status, getResponse.statusText);
        }
    } catch (error) {
        console.error('❌ GET搜索错误:', error);
    }
    
    return null;
}

// 检查前端状态
function checkFrontendState() {
    console.log('🔍 检查前端状态...');
    
    // 检查搜索输入框
    const searchInput = document.querySelector('input[placeholder*="搜索"]');
    if (searchInput) {
        console.log('✅ 找到搜索输入框:', searchInput);
        console.log('   - 值:', searchInput.value);
        console.log('   - 禁用状态:', searchInput.disabled);
    } else {
        console.log('❌ 未找到搜索输入框');
    }
    
    // 检查搜索按钮
    const searchButton = document.querySelector('button[type="submit"]');
    if (searchButton) {
        console.log('✅ 找到搜索按钮:', searchButton);
        console.log('   - 文本:', searchButton.textContent);
        console.log('   - 禁用状态:', searchButton.disabled);
    } else {
        console.log('❌ 未找到搜索按钮');
    }
    
    // 检查是否有错误信息
    const errorElements = document.querySelectorAll('[class*="error"], [class*="Error"]');
    if (errorElements.length > 0) {
        console.log('⚠️ 发现错误元素:', errorElements);
    }
    
    // 检查React DevTools
    if (window.__REACT_DEVTOOLS_GLOBAL_HOOK__) {
        console.log('✅ React DevTools 可用');
    } else {
        console.log('❌ React DevTools 不可用');
    }
}

// 模拟用户搜索
function simulateSearch(query = '流浪地球') {
    console.log(`🎭 模拟用户搜索: "${query}"`);
    
    const searchInput = document.querySelector('input[placeholder*="搜索"]');
    const searchButton = document.querySelector('button[type="submit"]');
    
    if (!searchInput || !searchButton) {
        console.error('❌ 无法找到搜索元素');
        return;
    }
    
    // 设置输入值
    searchInput.value = query;
    searchInput.dispatchEvent(new Event('input', { bubbles: true }));
    searchInput.dispatchEvent(new Event('change', { bubbles: true }));
    
    console.log('📝 已设置搜索关键词');
    
    // 点击搜索按钮
    setTimeout(() => {
        console.log('🖱️ 点击搜索按钮');
        searchButton.click();
    }, 100);
}

// 导出函数到全局
window.debugSearch = {
    testApiConnection,
    testSearch,
    checkFrontendState,
    simulateSearch
};

console.log('🎯 调试工具已准备就绪！');
console.log('使用方法:');
console.log('  debugSearch.testApiConnection() - 测试API连接');
console.log('  debugSearch.testSearch("关键词") - 测试搜索API');
console.log('  debugSearch.checkFrontendState() - 检查前端状态');
console.log('  debugSearch.simulateSearch("关键词") - 模拟用户搜索');
