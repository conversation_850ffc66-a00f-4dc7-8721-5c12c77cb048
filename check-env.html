<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>环境变量检查</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .env-check {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>环境变量检查</h1>
    
    <div id="env-results"></div>
    
    <script>
        function checkEnvironment() {
            const results = document.getElementById('env-results');
            let html = '';
            
            // 检查是否在开发环境
            const isDev = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
            html += `<div class="env-check ${isDev ? 'success' : 'warning'}">
                <h3>运行环境</h3>
                <p>当前环境: ${isDev ? '开发环境' : '生产环境'}</p>
                <p>主机名: ${window.location.hostname}</p>
                <p>端口: ${window.location.port}</p>
            </div>`;
            
            // 检查Vite环境变量（这些在浏览器中不可见，但我们可以测试API调用）
            html += `<div class="env-check">
                <h3>环境变量测试</h3>
                <p>注意: Vite环境变量在浏览器中不直接可见，但我们可以通过API调用测试它们是否正确配置。</p>
                <button onclick="testSupabaseConnection()">测试Supabase连接</button>
                <div id="supabase-test-result"></div>
            </div>`;
            
            // 检查浏览器支持
            const features = {
                'Fetch API': typeof fetch !== 'undefined',
                'Promise': typeof Promise !== 'undefined',
                'ES6 Modules': typeof Symbol !== 'undefined',
                'Local Storage': typeof localStorage !== 'undefined',
                'Session Storage': typeof sessionStorage !== 'undefined'
            };
            
            let featuresHtml = '<h3>浏览器功能支持</h3><ul>';
            for (const [feature, supported] of Object.entries(features)) {
                featuresHtml += `<li class="${supported ? 'success' : 'error'}">${feature}: ${supported ? '✅' : '❌'}</li>`;
            }
            featuresHtml += '</ul>';
            
            html += `<div class="env-check">${featuresHtml}</div>`;
            
            // 检查网络连接
            html += `<div class="env-check">
                <h3>网络连接测试</h3>
                <button onclick="testNetworkConnection()">测试网络连接</button>
                <div id="network-test-result"></div>
            </div>`;
            
            results.innerHTML = html;
        }
        
        async function testSupabaseConnection() {
            const resultDiv = document.getElementById('supabase-test-result');
            resultDiv.innerHTML = '<p>正在测试Supabase连接...</p>';
            
            try {
                // 尝试连接到Supabase Edge Function
                const response = await fetch('https://poijzwpvytxbhssbrrew.supabase.co/functions/v1/movie-search?query=test&page=1&type=multi', {
                    method: 'GET',
                    headers: {
                        'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBvaWp6d3B2eXR4Ymhzc2JycmV3Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA3NTE1ODksImV4cCI6MjA2NjMyNzU4OX0.PiPSm8IepAF1_zj4Oa0h8hWmoh1OAm-06Ths-6iOuhE',
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h4>✅ Supabase连接成功</h4>
                            <p>状态码: ${response.status}</p>
                            <p>响应时间: ${Date.now() - startTime}ms</p>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    const errorText = await response.text();
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h4>❌ Supabase连接失败</h4>
                            <p>状态码: ${response.status}</p>
                            <p>错误信息: ${errorText}</p>
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ Supabase连接错误</h4>
                        <p>错误信息: ${error.message}</p>
                    </div>
                `;
            }
        }
        
        async function testNetworkConnection() {
            const resultDiv = document.getElementById('network-test-result');
            resultDiv.innerHTML = '<p>正在测试网络连接...</p>';
            
            const tests = [
                { name: 'Google DNS', url: 'https://8.8.8.8' },
                { name: 'Cloudflare DNS', url: 'https://1.1.1.1' },
                { name: 'GitHub', url: 'https://api.github.com' },
                { name: 'TMDB API', url: 'https://api.themoviedb.org/3' }
            ];
            
            let resultsHtml = '<h4>网络连接测试结果</h4><ul>';
            
            for (const test of tests) {
                try {
                    const startTime = Date.now();
                    const response = await fetch(test.url, { 
                        method: 'HEAD',
                        mode: 'no-cors',
                        cache: 'no-cache'
                    });
                    const duration = Date.now() - startTime;
                    resultsHtml += `<li class="success">${test.name}: ✅ (${duration}ms)</li>`;
                } catch (error) {
                    resultsHtml += `<li class="error">${test.name}: ❌ (${error.message})</li>`;
                }
            }
            
            resultsHtml += '</ul>';
            resultDiv.innerHTML = resultsHtml;
        }
        
        // 页面加载时自动检查
        window.onload = checkEnvironment;
    </script>
</body>
</html>
