<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>直接API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
        }
        .success {
            background: #d4edda;
            color: #155724;
        }
        .loading {
            background: #fff3cd;
            color: #856404;
        }
        input {
            width: 300px;
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>直接API测试</h1>
    
    <div class="test-section">
        <h2>基础连接测试</h2>
        <button onclick="testBasicConnection()">测试基础连接</button>
        <div id="basic-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>搜索API测试</h2>
        <input type="text" id="search-input" placeholder="输入搜索关键词" value="流浪地球">
        <br>
        <button onclick="testPostSearch()">POST搜索</button>
        <button onclick="testGetSearch()">GET搜索</button>
        <button onclick="testSupabaseClient()">Supabase客户端</button>
        <div id="search-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>环境变量测试</h2>
        <button onclick="testEnvironmentVars()">检查环境变量</button>
        <div id="env-result" class="result"></div>
    </div>

    <script>
        const SUPABASE_URL = 'https://poijzwpvytxbhssbrrew.supabase.co';
        const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBvaWp6d3B2eXR4Ymhzc2JycmV3Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA3NTE1ODksImV4cCI6MjA2NjMyNzU4OX0.PiPSm8IepAF1_zj4Oa0h8hWmoh1OAm-06Ths-6iOuhE';

        function setResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `result ${type}`;
        }

        async function testBasicConnection() {
            setResult('basic-result', '正在测试基础连接...', 'loading');
            
            try {
                const startTime = Date.now();
                const response = await fetch(`${SUPABASE_URL}/functions/v1/movie-search?query=test&page=1&type=multi`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
                        'Content-Type': 'application/json'
                    }
                });
                const duration = Date.now() - startTime;

                if (response.ok) {
                    const data = await response.json();
                    setResult('basic-result', 
                        `✅ 基础连接成功！\n` +
                        `耗时: ${duration}ms\n` +
                        `状态: ${response.status}\n` +
                        `响应: ${JSON.stringify(data, null, 2)}`, 
                        'success'
                    );
                } else {
                    const errorText = await response.text();
                    setResult('basic-result', 
                        `❌ 基础连接失败！\n` +
                        `耗时: ${duration}ms\n` +
                        `状态: ${response.status}\n` +
                        `错误: ${errorText}`, 
                        'error'
                    );
                }
            } catch (error) {
                setResult('basic-result', `❌ 连接错误: ${error.message}`, 'error');
            }
        }

        async function testPostSearch() {
            const query = document.getElementById('search-input').value;
            if (!query.trim()) {
                setResult('search-result', '请输入搜索关键词', 'error');
                return;
            }

            setResult('search-result', '正在使用POST方式搜索...', 'loading');
            
            try {
                const startTime = Date.now();
                const response = await fetch(`${SUPABASE_URL}/functions/v1/movie-search`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        query: query.trim(),
                        page: 1,
                        type: 'multi',
                        language: 'zh-CN',
                        include_adult: false
                    })
                });
                const duration = Date.now() - startTime;

                if (response.ok) {
                    const data = await response.json();
                    setResult('search-result', 
                        `✅ POST搜索成功！\n` +
                        `耗时: ${duration}ms\n` +
                        `找到 ${data.data?.results?.length || 0} 个结果\n` +
                        `响应: ${JSON.stringify(data, null, 2)}`, 
                        'success'
                    );
                } else {
                    const errorText = await response.text();
                    setResult('search-result', 
                        `❌ POST搜索失败！\n` +
                        `耗时: ${duration}ms\n` +
                        `状态: ${response.status}\n` +
                        `错误: ${errorText}`, 
                        'error'
                    );
                }
            } catch (error) {
                setResult('search-result', `❌ POST搜索错误: ${error.message}`, 'error');
            }
        }

        async function testGetSearch() {
            const query = document.getElementById('search-input').value;
            if (!query.trim()) {
                setResult('search-result', '请输入搜索关键词', 'error');
                return;
            }

            setResult('search-result', '正在使用GET方式搜索...', 'loading');
            
            try {
                const startTime = Date.now();
                const params = new URLSearchParams({
                    query: query.trim(),
                    page: '1',
                    type: 'multi',
                    language: 'zh-CN',
                    include_adult: 'false'
                });

                const response = await fetch(`${SUPABASE_URL}/functions/v1/movie-search?${params}`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
                        'Content-Type': 'application/json'
                    }
                });
                const duration = Date.now() - startTime;

                if (response.ok) {
                    const data = await response.json();
                    setResult('search-result', 
                        `✅ GET搜索成功！\n` +
                        `耗时: ${duration}ms\n` +
                        `找到 ${data.data?.results?.length || 0} 个结果\n` +
                        `响应: ${JSON.stringify(data, null, 2)}`, 
                        'success'
                    );
                } else {
                    const errorText = await response.text();
                    setResult('search-result', 
                        `❌ GET搜索失败！\n` +
                        `耗时: ${duration}ms\n` +
                        `状态: ${response.status}\n` +
                        `错误: ${errorText}`, 
                        'error'
                    );
                }
            } catch (error) {
                setResult('search-result', `❌ GET搜索错误: ${error.message}`, 'error');
            }
        }

        async function testSupabaseClient() {
            setResult('search-result', '正在测试Supabase客户端...', 'loading');
            
            // 这里我们无法直接使用Supabase客户端，因为它需要在React环境中
            // 但我们可以模拟相同的请求
            setResult('search-result', 
                '⚠️ Supabase客户端测试需要在React应用中进行\n' +
                '请在浏览器开发者工具中查看实际的网络请求', 
                'warning'
            );
        }

        function testEnvironmentVars() {
            setResult('env-result', 
                `环境变量检查:\n` +
                `SUPABASE_URL: ${SUPABASE_URL}\n` +
                `SUPABASE_ANON_KEY: ${SUPABASE_ANON_KEY.substring(0, 50)}...\n` +
                `\n注意: 在生产环境中，这些值应该从环境变量中读取`, 
                'success'
            );
        }
    </script>
</body>
</html>
