<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>搜索功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
            white-space: pre-wrap;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
        }
        .success {
            background: #d4edda;
            color: #155724;
        }
        .loading {
            background: #fff3cd;
            color: #856404;
        }
    </style>
</head>
<body>
    <h1>搜索功能测试</h1>
    
    <div class="test-section">
        <h2>API 连接测试</h2>
        <button onclick="testConnection()">测试连接</button>
        <div id="connection-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>搜索功能测试</h2>
        <input type="text" id="search-query" placeholder="输入搜索关键词" value="流浪地球">
        <button onclick="testSearch()">搜索</button>
        <button onclick="testSearchWithGet()">GET方式搜索</button>
        <div id="search-result" class="result"></div>
    </div>

    <script>
        const SUPABASE_URL = 'https://poijzwpvytxbhssbrrew.supabase.co';
        const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBvaWp6d3B2eXR4Ymhzc2JycmV3Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA3NTE1ODksImV4cCI6MjA2NjMyNzU4OX0.PiPSm8IepAF1_zj4Oa0h8hWmoh1OAm-06Ths-6iOuhE';

        function setResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `result ${type}`;
        }

        async function testConnection() {
            setResult('connection-result', '正在测试连接...', 'loading');
            
            try {
                const response = await fetch(`${SUPABASE_URL}/functions/v1/movie-search?query=test&page=1&type=multi`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    setResult('connection-result', `连接成功！状态: ${response.status}\n响应: ${JSON.stringify(data, null, 2)}`, 'success');
                } else {
                    const errorText = await response.text();
                    setResult('connection-result', `连接失败！状态: ${response.status}\n错误: ${errorText}`, 'error');
                }
            } catch (error) {
                setResult('connection-result', `连接错误: ${error.message}`, 'error');
            }
        }

        async function testSearch() {
            const query = document.getElementById('search-query').value;
            if (!query.trim()) {
                setResult('search-result', '请输入搜索关键词', 'error');
                return;
            }

            setResult('search-result', '正在搜索...', 'loading');
            
            try {
                const startTime = Date.now();
                
                // 使用POST方式（模拟Supabase客户端）
                const response = await fetch(`${SUPABASE_URL}/functions/v1/movie-search`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        query: query.trim(),
                        page: 1,
                        type: 'multi',
                        language: 'zh-CN',
                        include_adult: false
                    })
                });

                const endTime = Date.now();
                const duration = endTime - startTime;

                if (response.ok) {
                    const data = await response.json();
                    setResult('search-result', 
                        `搜索成功！耗时: ${duration}ms\n` +
                        `找到 ${data.data?.results?.length || 0} 个结果\n` +
                        `响应: ${JSON.stringify(data, null, 2)}`, 
                        'success'
                    );
                } else {
                    const errorText = await response.text();
                    setResult('search-result', 
                        `搜索失败！耗时: ${duration}ms\n状态: ${response.status}\n错误: ${errorText}`, 
                        'error'
                    );
                }
            } catch (error) {
                setResult('search-result', `搜索错误: ${error.message}`, 'error');
            }
        }

        async function testSearchWithGet() {
            const query = document.getElementById('search-query').value;
            if (!query.trim()) {
                setResult('search-result', '请输入搜索关键词', 'error');
                return;
            }

            setResult('search-result', '正在使用GET方式搜索...', 'loading');
            
            try {
                const startTime = Date.now();
                
                const params = new URLSearchParams({
                    query: query.trim(),
                    page: '1',
                    type: 'multi',
                    language: 'zh-CN',
                    include_adult: 'false'
                });

                const response = await fetch(`${SUPABASE_URL}/functions/v1/movie-search?${params}`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
                        'Content-Type': 'application/json'
                    }
                });

                const endTime = Date.now();
                const duration = endTime - startTime;

                if (response.ok) {
                    const data = await response.json();
                    setResult('search-result', 
                        `GET搜索成功！耗时: ${duration}ms\n` +
                        `找到 ${data.data?.results?.length || 0} 个结果\n` +
                        `响应: ${JSON.stringify(data, null, 2)}`, 
                        'success'
                    );
                } else {
                    const errorText = await response.text();
                    setResult('search-result', 
                        `GET搜索失败！耗时: ${duration}ms\n状态: ${response.status}\n错误: ${errorText}`, 
                        'error'
                    );
                }
            } catch (error) {
                setResult('search-result', `GET搜索错误: ${error.message}`, 'error');
            }
        }
    </script>
</body>
</html>
