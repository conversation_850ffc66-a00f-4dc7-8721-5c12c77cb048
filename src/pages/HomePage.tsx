import React, { useState, useCallback, useEffect } from 'react';
import SearchSection from '../components/SearchSection';
import TrendingSection from '../components/TrendingSection';
import SearchResults from '../components/SearchResults';
import SearchDebugPanel from '../components/SearchDebugPanel';
import { searchMoviesWithFallback, type MovieSearchResult } from '../lib/movieApi';
import { searchHistoryManager } from '../lib/searchHistory';

interface SearchResult {
  media_id: string;
  title: string;
  original_title: string;
  year: number;
  poster_url: string;
  genres: string[];
  vote_average?: number;
  popularity?: number;
  media_type?: 'movie' | 'tv';
}

const HomePage: React.FC = () => {
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [showDebugPanel, setShowDebugPanel] = useState(false);

  const handleSearch = useCallback(async (query: string) => {
    const trimmedQuery = query.trim();
    console.log(`🔍 开始处理搜索请求: "${trimmedQuery}"`);

    // 基本验证
    if (!trimmedQuery) {
      console.log('❌ 搜索关键词为空');
      setError('请输入搜索关键词');
      return;
    }

    if (trimmedQuery.length < 2) {
      console.log('❌ 搜索关键词太短');
      setError('搜索关键词至少需要2个字符');
      return;
    }

    console.log('✅ 搜索参数验证通过，开始搜索');
    setSearchQuery(trimmedQuery);
    setIsSearching(true);
    setError(null);
    setSearchResults([]); // 清空之前的结果

    const searchStartTime = Date.now();

    try {
      console.log(`开始搜索: "${trimmedQuery}"`);

      // 使用智能搜索（带降级机制）
      const response = await searchMoviesWithFallback({
        query: trimmedQuery,
        type: 'multi',
        page: 1
      });

      // 转换API响应为SearchResults组件期望的格式
      const convertedResults: SearchResult[] = response.data.results.map((movie: MovieSearchResult) => ({
        media_id: `${movie.media_type}-${movie.id}`,
        title: movie.title,
        original_title: movie.original_title,
        year: movie.release_date ? new Date(movie.release_date).getFullYear() : 0,
        poster_url: movie.poster_url || 'https://images.unsplash.com/photo-1478720568477-b0ac8e6c4a17',
        genres: [], // 将在SearchResults组件中从genre_ids映射
        genre_ids: movie.genre_ids,
        vote_average: movie.vote_average,
        vote_count: movie.vote_count,
        popularity: movie.popularity,
        media_type: movie.media_type as 'movie' | 'tv',
        release_date: movie.release_date
      }));

      setSearchResults(convertedResults);

      // 保存搜索历史，包含结果数量
      searchHistoryManager.addSearch(trimmedQuery, convertedResults.length, 'multi');

      const searchEndTime = Date.now();
      const searchDuration = searchEndTime - searchStartTime;

      console.log(`搜索完成: "${trimmedQuery}", 找到 ${convertedResults.length} 个结果, 耗时 ${searchDuration}ms`);

      if (response.cached) {
        console.log('使用了缓存结果');
      }

      // 如果没有结果，提供友好提示
      if (convertedResults.length === 0) {
        setError(`没有找到与"${trimmedQuery}"相关的影视作品，请尝试其他关键词`);
      }
    } catch (err) {
      const searchEndTime = Date.now();
      const searchDuration = searchEndTime - searchStartTime;

      console.error(`搜索失败: "${trimmedQuery}", 耗时 ${searchDuration}ms`, err);

      let errorMessage = '搜索失败，请稍后重试';
      if (err instanceof Error) {
        errorMessage = err.message;
      }

      setError(errorMessage);
      setSearchResults([]);
    } finally {
      console.log('🔄 搜索完成，重置搜索状态');
      setIsSearching(false);
    }
  }, []);

  const handleClearError = useCallback(() => {
    setError(null);
  }, []);

  // 在开发环境下加载测试工具和错误监听
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      // 监听未捕获的Promise rejection
      const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
        console.error('🚨 未捕获的Promise rejection:', event.reason);
        console.error('🚨 Promise:', event.promise);
      };

      // 监听未捕获的错误
      const handleError = (event: ErrorEvent) => {
        console.error('🚨 未捕获的错误:', event.error);
        console.error('🚨 错误信息:', event.message);
        console.error('🚨 文件:', event.filename);
        console.error('🚨 行号:', event.lineno);
      };

      window.addEventListener('unhandledrejection', handleUnhandledRejection);
      window.addEventListener('error', handleError);

      import('../utils/searchTest').then(module => {
        console.log('🔍 搜索测试工具已加载');
        console.log('使用 runSearchTest() 来测试搜索功能');
        (window as any).runSearchTest = module.runTestInConsole;
      }).catch(error => {
        console.warn('搜索测试工具加载失败:', error);
      });

      return () => {
        window.removeEventListener('unhandledrejection', handleUnhandledRejection);
        window.removeEventListener('error', handleError);
      };
    }
  }, []);

  return (
    <div className="min-h-screen">
      <SearchSection
        onSearch={handleSearch}
        isSearching={isSearching}
        error={error}
        onClearError={handleClearError}
      />

      {searchResults.length > 0 ? (
        <SearchResults
          results={searchResults}
          query={searchQuery}
          isLoading={isSearching}
        />
      ) : (
        <TrendingSection />
      )}

      {/* 调试面板 - 仅在开发环境显示 */}
      {process.env.NODE_ENV === 'development' && (
        <SearchDebugPanel
          isVisible={showDebugPanel}
          onToggle={() => setShowDebugPanel(!showDebugPanel)}
        />
      )}
    </div>
  );
};

export default HomePage;