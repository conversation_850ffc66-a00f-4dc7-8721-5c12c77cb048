import React, { useState, useCallback } from 'react';
import SearchSection from '../components/SearchSection';
import TrendingSection from '../components/TrendingSection';
import SearchResults from '../components/SearchResults';
import { searchMovies, type MovieSearchResult } from '../lib/movieApi';
import { searchHistoryManager } from '../lib/searchHistory';

interface SearchResult {
  media_id: string;
  title: string;
  original_title: string;
  year: number;
  poster_url: string;
  genres: string[];
  vote_average?: number;
  popularity?: number;
  media_type?: 'movie' | 'tv';
}

const HomePage: React.FC = () => {
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [error, setError] = useState<string | null>(null);

  const handleSearch = useCallback(async (query: string) => {
    setSearchQuery(query);
    setIsSearching(true);
    setError(null);

    try {
      // 使用真实的API调用
      const response = await searchMovies({
        query: query.trim(),
        type: 'multi',
        page: 1
      });

      // 转换API响应为SearchResults组件期望的格式
      const convertedResults: SearchResult[] = response.data.results.map((movie: MovieSearchResult) => ({
        media_id: `${movie.media_type}-${movie.id}`,
        title: movie.title,
        original_title: movie.original_title,
        year: movie.release_date ? new Date(movie.release_date).getFullYear() : 0,
        poster_url: movie.poster_url || 'https://images.unsplash.com/photo-1478720568477-b0ac8e6c4a17',
        genres: [], // 将在SearchResults组件中从genre_ids映射
        genre_ids: movie.genre_ids,
        vote_average: movie.vote_average,
        vote_count: movie.vote_count,
        popularity: movie.popularity,
        media_type: movie.media_type as 'movie' | 'tv',
        release_date: movie.release_date
      }));

      setSearchResults(convertedResults);

      // 保存搜索历史，包含结果数量
      searchHistoryManager.addSearch(query, convertedResults.length, 'multi');

      if (response.cached) {
        console.log('使用了缓存结果');
      }
    } catch (err) {
      console.error('Search error:', err);
      setError(err instanceof Error ? err.message : '搜索失败，请稍后重试');
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  }, []);

  const handleClearError = useCallback(() => {
    setError(null);
  }, []);

  return (
    <div className="min-h-screen">
      <SearchSection
        onSearch={handleSearch}
        isSearching={isSearching}
        error={error}
        onClearError={handleClearError}
      />

      {searchResults.length > 0 ? (
        <SearchResults
          results={searchResults}
          query={searchQuery}
          isLoading={isSearching}
        />
      ) : (
        <TrendingSection />
      )}
    </div>
  );
};

export default HomePage;