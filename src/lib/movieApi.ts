/**
 * 电影API服务
 * 封装对Supabase Edge Functions的调用
 */

import { supabase } from './supabase'

// 类型定义
export interface MovieSearchParams {
  query: string
  page?: number
  type?: 'movie' | 'tv' | 'multi'
  language?: string
  include_adult?: boolean
}

export interface MovieSearchResult {
  id: number
  title: string
  original_title: string
  overview: string
  poster_url: string | null
  backdrop_url: string | null
  release_date: string | null
  vote_average: number
  vote_count: number
  popularity: number
  genre_ids: number[]
  media_type: string
  language: string
}

export interface MovieSearchResponse {
  success: boolean
  data: {
    page: number
    results: MovieSearchResult[]
    total_pages: number
    total_results: number
    query: string
  }
  cached: boolean
  timestamp: string
}

export interface MovieDetails {
  id: number
  title: string
  original_title: string
  overview: string
  poster_url: string | null
  backdrop_url: string | null
  release_date: string | null
  vote_average: number
  vote_count: number
  popularity: number
  genres: Array<{ id: number; name: string }>
  runtime: number | null
  budget: number | null
  revenue: number | null
  status: string
  tagline: string
  language: string
  imdb_id: string | null
  homepage: string | null
  production_companies: Array<{
    id: number
    name: string
    logo_url: string | null
    origin_country: string
  }>
  production_countries: Array<{
    code: string
    name: string
  }>
  spoken_languages: Array<{
    code: string
    name: string
    english_name: string
  }>
  collection: {
    id: number
    name: string
    poster_url: string | null
    backdrop_url: string | null
  } | null
  cast: Array<{
    id: number
    name: string
    character: string
    profile_url: string | null
    order: number
  }>
  crew: {
    directors: Array<{
      id: number
      name: string
      profile_url: string | null
    }>
    writers: Array<{
      id: number
      name: string
      profile_url: string | null
    }>
    producers: Array<{
      id: number
      name: string
      profile_url: string | null
    }>
  }
  media_type: 'movie' | 'tv'
  tv_info?: {
    number_of_episodes: number
    number_of_seasons: number
    episode_run_time: number[]
  }
}

export interface MovieDetailsResponse {
  success: boolean
  data: MovieDetails
  cached: boolean
  timestamp: string
}

export interface ApiError {
  error: string
  status?: number
}

/**
 * 搜索电影和电视剧
 */
export async function searchMovies(params: MovieSearchParams): Promise<MovieSearchResponse> {
  try {
    const { data, error } = await supabase.functions.invoke('movie-search', {
      body: {
        query: params.query,
        page: params.page || 1,
        type: params.type || 'multi',
        language: params.language || 'zh-CN',
        include_adult: params.include_adult || false
      }
    })

    if (error) {
      console.error('Movie search error:', error)
      throw new Error(error.message || '搜索失败')
    }

    if (!data.success) {
      throw new Error(data.error || '搜索失败')
    }

    return data
  } catch (error) {
    console.error('Search movies error:', error)
    throw error
  }
}

/**
 * 使用GET方式搜索电影（用于URL分享等场景）
 */
export async function searchMoviesWithGet(params: MovieSearchParams): Promise<MovieSearchResponse> {
  try {
    const searchParams = new URLSearchParams({
      query: params.query,
      page: String(params.page || 1),
      type: params.type || 'multi',
      language: params.language || 'zh-CN',
      include_adult: String(params.include_adult || false)
    })

    const response = await fetch(
      `${supabase.supabaseUrl}/functions/v1/movie-search?${searchParams}`,
      {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${supabase.supabaseKey}`,
          'Content-Type': 'application/json'
        }
      }
    )

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.error || `HTTP ${response.status}`)
    }

    const data = await response.json()
    
    if (!data.success) {
      throw new Error(data.error || '搜索失败')
    }

    return data
  } catch (error) {
    console.error('Search movies with GET error:', error)
    throw error
  }
}

/**
 * 获取电影或电视剧详情
 */
export async function getMovieDetails(id: number, type: 'movie' | 'tv' = 'movie'): Promise<MovieDetailsResponse> {
  try {
    const response = await fetch(
      `${supabase.supabaseUrl}/functions/v1/movie-details?id=${id}&type=${type}`,
      {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${supabase.supabaseKey}`,
          'Content-Type': 'application/json'
        }
      }
    )

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.error || `HTTP ${response.status}`)
    }

    const data = await response.json()
    
    if (!data.success) {
      throw new Error(data.error || '获取详情失败')
    }

    return data
  } catch (error) {
    console.error('Get movie details error:', error)
    throw error
  }
}

/**
 * 格式化电影时长
 */
export function formatRuntime(runtime: number | null): string {
  if (!runtime) return '未知'
  
  const hours = Math.floor(runtime / 60)
  const minutes = runtime % 60
  
  if (hours > 0) {
    return `${hours}小时${minutes}分钟`
  } else {
    return `${minutes}分钟`
  }
}

/**
 * 格式化发布日期
 */
export function formatReleaseDate(date: string | null): string {
  if (!date) return '未知'
  
  try {
    const dateObj = new Date(date)
    return dateObj.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  } catch {
    return date
  }
}

/**
 * 格式化评分
 */
export function formatRating(rating: number): string {
  return rating.toFixed(1)
}

/**
 * 获取海报URL（带默认图片）
 */
export function getPosterUrl(posterUrl: string | null, size: 'small' | 'medium' | 'large' = 'medium'): string {
  if (!posterUrl) {
    return '/placeholder-poster.jpg' // 需要添加默认海报图片
  }
  
  // 如果需要不同尺寸，可以替换URL中的尺寸参数
  const sizeMap = {
    small: 'w185',
    medium: 'w500',
    large: 'w780'
  }
  
  return posterUrl.replace(/w\d+/, sizeMap[size])
}

/**
 * 获取背景图URL
 */
export function getBackdropUrl(backdropUrl: string | null): string {
  if (!backdropUrl) {
    return '/placeholder-backdrop.jpg' // 需要添加默认背景图片
  }
  
  return backdropUrl
}
