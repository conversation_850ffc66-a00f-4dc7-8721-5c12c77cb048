/**
 * 电影API服务
 * 封装对Supabase Edge Functions的调用
 */

import { supabase } from './supabase'

// 类型定义
export interface MovieSearchParams {
  query: string
  page?: number
  type?: 'movie' | 'tv' | 'multi'
  language?: string
  include_adult?: boolean
}

export interface MovieSearchResult {
  id: number
  title: string
  original_title: string
  overview: string
  poster_url: string | null
  backdrop_url: string | null
  release_date: string | null
  vote_average: number
  vote_count: number
  popularity: number
  genre_ids: number[]
  media_type: string
  language: string
}

export interface MovieSearchResponse {
  success: boolean
  data: {
    page: number
    results: MovieSearchResult[]
    total_pages: number
    total_results: number
    query: string
  }
  cached: boolean
  timestamp: string
}

export interface MovieDetails {
  id: number
  title: string
  original_title: string
  overview: string
  poster_url: string | null
  backdrop_url: string | null
  release_date: string | null
  vote_average: number
  vote_count: number
  popularity: number
  genres: Array<{ id: number; name: string }>
  runtime: number | null
  budget: number | null
  revenue: number | null
  status: string
  tagline: string
  language: string
  imdb_id: string | null
  homepage: string | null
  production_companies: Array<{
    id: number
    name: string
    logo_url: string | null
    origin_country: string
  }>
  production_countries: Array<{
    code: string
    name: string
  }>
  spoken_languages: Array<{
    code: string
    name: string
    english_name: string
  }>
  collection: {
    id: number
    name: string
    poster_url: string | null
    backdrop_url: string | null
  } | null
  cast: Array<{
    id: number
    name: string
    character: string
    profile_url: string | null
    order: number
  }>
  crew: {
    directors: Array<{
      id: number
      name: string
      profile_url: string | null
    }>
    writers: Array<{
      id: number
      name: string
      profile_url: string | null
    }>
    producers: Array<{
      id: number
      name: string
      profile_url: string | null
    }>
  }
  media_type: 'movie' | 'tv'
  tv_info?: {
    number_of_episodes: number
    number_of_seasons: number
    episode_run_time: number[]
  }
}

export interface MovieDetailsResponse {
  success: boolean
  data: MovieDetails
  cached: boolean
  timestamp: string
}

export interface ApiError {
  error: string
  status?: number
}

/**
 * 创建带超时的AbortController
 */
function createTimeoutController(timeoutMs: number = 10000): AbortController {
  const controller = new AbortController();
  const timeoutId = setTimeout(() => {
    controller.abort();
  }, timeoutMs);

  // 清理定时器
  controller.signal.addEventListener('abort', () => {
    clearTimeout(timeoutId);
  });

  return controller;
}

/**
 * 延迟函数
 */
function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * 重试机制
 */
async function retryWithBackoff<T>(
  fn: () => Promise<T>,
  maxRetries: number = 3,
  baseDelay: number = 1000
): Promise<T> {
  let lastError: Error;

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error as Error;

      if (attempt === maxRetries) {
        break;
      }

      // 指数退避延迟
      const delayMs = baseDelay * Math.pow(2, attempt);
      console.warn(`API调用失败，${delayMs}ms后重试 (${attempt + 1}/${maxRetries}):`, error);
      await delay(delayMs);
    }
  }

  throw lastError!;
}

/**
 * 发送调试事件
 */
function emitSearchEvent(type: 'start' | 'end', details: any) {
  if (typeof window !== 'undefined') {
    window.dispatchEvent(new CustomEvent(`search-${type}`, { detail: details }));
  }
}

/**
 * 搜索电影和电视剧 - 优化版本
 */
export async function searchMovies(params: MovieSearchParams): Promise<MovieSearchResponse> {
  const startTime = Date.now();
  console.log('开始搜索:', params);

  // 发送搜索开始事件
  emitSearchEvent('start', { query: params.query });

  try {
    // 参数验证
    if (!params.query || params.query.trim().length === 0) {
      throw new Error('搜索关键词不能为空');
    }

    if (params.query.length > 100) {
      throw new Error('搜索关键词长度不能超过100个字符');
    }

    // 使用重试机制调用API
    const result = await retryWithBackoff(async () => {
      const controller = createTimeoutController(8000); // 8秒超时

      try {
        const { data, error } = await supabase.functions.invoke('movie-search', {
          body: {
            query: params.query.trim(),
            page: params.page || 1,
            type: params.type || 'multi',
            language: params.language || 'zh-CN',
            include_adult: params.include_adult || false
          },
          // 注意：Supabase客户端可能不支持AbortController，这里作为示例
        });

        if (controller.signal.aborted) {
          throw new Error('请求超时，请检查网络连接');
        }

        if (error) {
          console.error('Supabase函数调用错误:', error);
          throw new Error(error.message || '搜索服务暂时不可用');
        }

        if (!data) {
          throw new Error('服务器返回空数据');
        }

        if (!data.success) {
          throw new Error(data.error || '搜索失败');
        }

        return data;
      } catch (error) {
        if (controller.signal.aborted) {
          throw new Error('请求超时，请检查网络连接');
        }
        throw error;
      }
    }, 2, 1500); // 最多重试2次，基础延迟1.5秒

    const endTime = Date.now();
    console.log(`搜索完成，耗时: ${endTime - startTime}ms`);

    // 发送搜索成功事件
    emitSearchEvent('end', {
      query: params.query,
      success: true,
      resultCount: result.data.results.length,
      method: 'supabase',
      cached: result.cached
    });

    return result;
  } catch (error) {
    const endTime = Date.now();
    console.error(`搜索失败，耗时: ${endTime - startTime}ms`, error);

    // 发送搜索失败事件
    emitSearchEvent('end', {
      query: params.query,
      success: false,
      error: error instanceof Error ? error.message : '未知错误',
      method: 'supabase'
    });

    // 提供更友好的错误信息
    if (error instanceof Error) {
      if (error.message.includes('timeout') || error.message.includes('超时')) {
        throw new Error('搜索超时，请检查网络连接后重试');
      } else if (error.message.includes('network') || error.message.includes('fetch')) {
        throw new Error('网络连接异常，请检查网络后重试');
      } else if (error.message.includes('401') || error.message.includes('unauthorized')) {
        throw new Error('服务认证失败，请联系管理员');
      } else if (error.message.includes('429') || error.message.includes('rate limit')) {
        throw new Error('请求过于频繁，请稍后再试');
      } else if (error.message.includes('500') || error.message.includes('502') || error.message.includes('503')) {
        throw new Error('服务器暂时不可用，请稍后重试');
      }
    }

    throw error;
  }
}

/**
 * 使用GET方式搜索电影（降级方案）
 */
export async function searchMoviesWithGet(params: MovieSearchParams): Promise<MovieSearchResponse> {
  const startTime = Date.now();
  console.log('使用GET方式搜索:', params);

  try {
    const searchParams = new URLSearchParams({
      query: params.query.trim(),
      page: String(params.page || 1),
      type: params.type || 'multi',
      language: params.language || 'zh-CN',
      include_adult: String(params.include_adult || false)
    });

    const controller = createTimeoutController(10000); // 10秒超时

    const response = await fetch(
      `${supabase.supabaseUrl}/functions/v1/movie-search?${searchParams}`,
      {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${supabase.supabaseKey}`,
          'Content-Type': 'application/json',
          'User-Agent': 'NarratoAI/1.0'
        },
        signal: controller.signal
      }
    );

    if (!response.ok) {
      let errorMessage = `HTTP ${response.status}`;
      try {
        const errorData = await response.json();
        errorMessage = errorData.error || errorMessage;
      } catch {
        // 忽略JSON解析错误，使用默认错误信息
      }
      throw new Error(errorMessage);
    }

    const data = await response.json();

    if (!data.success) {
      throw new Error(data.error || '搜索失败');
    }

    const endTime = Date.now();
    console.log(`GET搜索完成，耗时: ${endTime - startTime}ms`);

    // 发送搜索成功事件
    emitSearchEvent('end', {
      query: params.query,
      success: true,
      resultCount: data.data.results.length,
      method: 'fetch',
      cached: data.cached
    });

    return data;
  } catch (error) {
    const endTime = Date.now();
    console.error(`GET搜索失败，耗时: ${endTime - startTime}ms`, error);

    // 发送搜索失败事件
    emitSearchEvent('end', {
      query: params.query,
      success: false,
      error: error instanceof Error ? error.message : '未知错误',
      method: 'fetch'
    });

    throw error;
  }
}

/**
 * 智能搜索 - 自动降级的搜索方法
 */
export async function searchMoviesWithFallback(params: MovieSearchParams): Promise<MovieSearchResponse> {
  const startTime = Date.now();

  try {
    // 首先尝试使用Supabase客户端
    console.log('尝试使用Supabase客户端搜索');
    return await searchMovies(params);
  } catch (error) {
    console.warn('Supabase客户端搜索失败，尝试GET方式:', error);

    try {
      // 降级到GET请求
      console.log('降级到GET请求');
      const result = await searchMoviesWithGet(params);

      // 发送降级成功事件
      emitSearchEvent('end', {
        query: params.query,
        success: true,
        resultCount: result.data.results.length,
        method: 'fallback',
        cached: result.cached
      });

      return result;
    } catch (fallbackError) {
      const endTime = Date.now();
      console.error(`所有搜索方法都失败了，耗时: ${endTime - startTime}ms`, fallbackError);

      // 发送最终失败事件
      emitSearchEvent('end', {
        query: params.query,
        success: false,
        error: fallbackError instanceof Error ? fallbackError.message : '所有搜索方法都失败',
        method: 'fallback'
      });

      // 如果都失败了，返回一个更友好的错误
      throw new Error('搜索服务暂时不可用，请稍后重试。如果问题持续存在，请联系技术支持。');
    }
  }
}

/**
 * 获取电影或电视剧详情
 */
export async function getMovieDetails(id: number, type: 'movie' | 'tv' = 'movie'): Promise<MovieDetailsResponse> {
  try {
    const response = await fetch(
      `${supabase.supabaseUrl}/functions/v1/movie-details?id=${id}&type=${type}`,
      {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${supabase.supabaseKey}`,
          'Content-Type': 'application/json'
        }
      }
    )

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.error || `HTTP ${response.status}`)
    }

    const data = await response.json()
    
    if (!data.success) {
      throw new Error(data.error || '获取详情失败')
    }

    return data
  } catch (error) {
    console.error('Get movie details error:', error)
    throw error
  }
}

/**
 * 格式化电影时长
 */
export function formatRuntime(runtime: number | null): string {
  if (!runtime) return '未知'
  
  const hours = Math.floor(runtime / 60)
  const minutes = runtime % 60
  
  if (hours > 0) {
    return `${hours}小时${minutes}分钟`
  } else {
    return `${minutes}分钟`
  }
}

/**
 * 格式化发布日期
 */
export function formatReleaseDate(date: string | null): string {
  if (!date) return '未知'
  
  try {
    const dateObj = new Date(date)
    return dateObj.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  } catch {
    return date
  }
}

/**
 * 格式化评分
 */
export function formatRating(rating: number): string {
  return rating.toFixed(1)
}

/**
 * 获取海报URL（带默认图片）
 */
export function getPosterUrl(posterUrl: string | null, size: 'small' | 'medium' | 'large' = 'medium'): string {
  if (!posterUrl) {
    return '/placeholder-poster.jpg' // 需要添加默认海报图片
  }
  
  // 如果需要不同尺寸，可以替换URL中的尺寸参数
  const sizeMap = {
    small: 'w185',
    medium: 'w500',
    large: 'w780'
  }
  
  return posterUrl.replace(/w\d+/, sizeMap[size])
}

/**
 * 获取背景图URL
 */
export function getBackdropUrl(backdropUrl: string | null): string {
  if (!backdropUrl) {
    return '/placeholder-backdrop.jpg' // 需要添加默认背景图片
  }
  
  return backdropUrl
}
